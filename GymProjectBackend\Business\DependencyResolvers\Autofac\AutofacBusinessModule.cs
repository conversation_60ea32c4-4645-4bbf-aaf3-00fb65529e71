using Autofac;
using Autofac.Extras.DynamicProxy;
using Business.Abstract;
using Business.Concrete;
using Castle.DynamicProxy;
using Core.Utilities.Interceptors;
using Core.Utilities.Security.JWT;
using Core.Utilities.Security.CompanyContext;
using DataAccess.Abstract;
using DataAccess.Concrete.EntityFramework;
using Microsoft.AspNetCore.Http;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Business.DependencyResolvers.Autofac
{
    public class AutofacBusinessModule:Module
    {
        protected override void Load(ContainerBuilder builder)
        {
            // GymContext - Scoped lifetime for proper DbContext management
            builder.RegisterType<GymContext>().AsSelf().InstancePerLifetimeScope();

            // Business Services - Scoped lifetime for multi-tenant safety
            builder.RegisterType<UserManager>().As<IUserService>().InstancePerLifetimeScope();
            builder.RegisterType<CompanyManager>().As<ICompanyService>().InstancePerLifetimeScope();
            builder.RegisterType<CompanyAdressManager>().As<ICompanyAdressService>().InstancePerLifetimeScope();
            builder.RegisterType<MemberManager>().As<IMemberService>().InstancePerLifetimeScope();
            builder.RegisterType<MembershipTypeManager>().As<IMembershipTypeService>().InstancePerLifetimeScope();
            builder.RegisterType<MembershipManager>().As<IMembershipService>().InstancePerLifetimeScope();
            builder.RegisterType<CompanyUserManager>().As<ICompanyUserService>().InstancePerLifetimeScope();
            builder.RegisterType<PaymentManager>().As<IPaymentService>().InstancePerLifetimeScope();
            builder.RegisterType<UserCompanyManager>().As<IUserCompanyService>().InstancePerLifetimeScope();
            builder.RegisterType<UnifiedCompanyManager>().As<IUnifiedCompanyService>().InstancePerLifetimeScope();
            builder.RegisterType<EntryExitHistoryManager>().As<IEntryExitHistoryService>().InstancePerLifetimeScope();
            builder.RegisterType<CityManager>().As<ICityService>().InstancePerLifetimeScope();
            builder.RegisterType<TownManager>().As<ITownService>().InstancePerLifetimeScope();
            builder.RegisterType<ProductManager>().As<IProductService>().InstancePerLifetimeScope();
            builder.RegisterType<TransactionManager>().As<ITransactionService>().InstancePerLifetimeScope();
            builder.RegisterType<OperationClaimManager>().As<IOperationClaimService>().InstancePerLifetimeScope();
            builder.RegisterType<UserOperationClaimManager>().As<IUserOperationClaimService>().InstancePerLifetimeScope();
            builder.RegisterType<RemainingDebtManager>().As<IRemainingDebtService>().InstancePerLifetimeScope();
            builder.RegisterType<DebtPaymentManager>().As<IDebtPaymentService>().InstancePerLifetimeScope();

            // DAL Services - Scoped lifetime for proper DbContext injection
            builder.RegisterType<EfUserDal>().As<IUserDal>().InstancePerLifetimeScope();
            builder.RegisterType<EfCompanyDal>().As<ICompanyDal>().InstancePerLifetimeScope();
            builder.RegisterType<EfCompanyAdressDal>().As<ICompanyAdressDal>().InstancePerLifetimeScope();
            builder.RegisterType<EfMemberDal>().As<IMemberDal>().InstancePerLifetimeScope();
            builder.RegisterType<EfMembershipTypeDal>().As<IMembershiptypeDal>().InstancePerLifetimeScope();
            builder.RegisterType<EfMembershipDal>().As<IMembershipDal>().InstancePerLifetimeScope();
            builder.RegisterType<EfCompanyUserDal>().As<ICompanyUserDal>().InstancePerLifetimeScope();
            builder.RegisterType<EfPaymentDal>().As<IPaymentDal>().InstancePerLifetimeScope();
            builder.RegisterType<EfUserCompanyDal>().As<IUserCompanyDal>().InstancePerLifetimeScope();
            builder.RegisterType<EfEntryExitHistoryDal>().As<IEntryExitHistoryDal>().InstancePerLifetimeScope();
            builder.RegisterType<EfCityDal>().As<ICityDal>().InstancePerLifetimeScope();
            builder.RegisterType<EfTownDal>().As<ITownDal>().InstancePerLifetimeScope();
            builder.RegisterType<EfProductDal>().As<IProductDal>().InstancePerLifetimeScope();
            builder.RegisterType<EfTransactionDal>().As<ITransactionDal>().InstancePerLifetimeScope();
            builder.RegisterType<EfOperationClaimDal>().As<IOperationClaimDal>().InstancePerLifetimeScope();
            builder.RegisterType<EfUserOperationClaimDal>().As<IUserOperationClaimDal>().InstancePerLifetimeScope();
            builder.RegisterType<EfRemainingDebtDal>().As<IRemainingDebtDal>().InstancePerLifetimeScope();
            builder.RegisterType<EfDebtPaymentDal>().As<IDebtPaymentDal>().InstancePerLifetimeScope();

            // Stateless Services - Singleton lifetime (safe for multi-tenant)
            builder.RegisterType<AuthManager>().As<IAuthService>().InstancePerLifetimeScope(); // AuthManager state tutabilir
            builder.RegisterType<JwtHelper>().As<ITokenHelper>().SingleInstance(); // Stateless
            builder.RegisterType<HttpContextAccessor>().As<IHttpContextAccessor>().SingleInstance(); // Framework service

            // Multi-tenant Company Context - REMOVED (CoreModule'de zaten var, çakışma önlendi)

            // Additional Business Services - Scoped lifetime
            builder.RegisterType<UserDeviceManager>().As<IUserDeviceService>().InstancePerLifetimeScope();
            builder.RegisterType<MembershipFreezeHistoryManager>().As<IMembershipFreezeHistoryService>().InstancePerLifetimeScope();
            builder.RegisterType<LicensePackageManager>().As<ILicensePackageService>().InstancePerLifetimeScope();
            builder.RegisterType<UserLicenseManager>().As<IUserLicenseService>().InstancePerLifetimeScope();
            builder.RegisterType<LicenseTransactionManager>().As<ILicenseTransactionService>().InstancePerLifetimeScope();
            builder.RegisterType<ExpenseManager>().As<IExpenseService>().InstancePerLifetimeScope();
            builder.RegisterType<ProfileManager>().As<IProfileService>().InstancePerLifetimeScope();
            builder.RegisterType<ExerciseCategoryManager>().As<IExerciseCategoryService>().InstancePerLifetimeScope();
            builder.RegisterType<SystemExerciseManager>().As<ISystemExerciseService>().InstancePerLifetimeScope();
            builder.RegisterType<CompanyExerciseManager>().As<ICompanyExerciseService>().InstancePerLifetimeScope();
            builder.RegisterType<WorkoutProgramTemplateManager>().As<IWorkoutProgramTemplateService>().InstancePerLifetimeScope();
            builder.RegisterType<MemberWorkoutProgramManager>().As<IMemberWorkoutProgramService>().InstancePerLifetimeScope();

            // Additional DAL Services - Scoped lifetime
            builder.RegisterType<EfUserDeviceDal>().As<IUserDeviceDal>().InstancePerLifetimeScope();
            builder.RegisterType<EfMembershipFreezeHistoryDal>().As<IMembershipFreezeHistoryDal>().InstancePerLifetimeScope();
            builder.RegisterType<EfLicensePackageDal>().As<ILicensePackageDal>().InstancePerLifetimeScope();
            builder.RegisterType<EfUserLicenseDal>().As<IUserLicenseDal>().InstancePerLifetimeScope();
            builder.RegisterType<EfLicenseTransactionDal>().As<ILicenseTransactionDal>().InstancePerLifetimeScope();
            builder.RegisterType<EfExpenseDal>().As<IExpenseDal>().InstancePerLifetimeScope();
            builder.RegisterType<EfExerciseCategoryDal>().As<IExerciseCategoryDal>().InstancePerLifetimeScope();
            builder.RegisterType<EfSystemExerciseDal>().As<ISystemExerciseDal>().InstancePerLifetimeScope();
            builder.RegisterType<EfCompanyExerciseDal>().As<ICompanyExerciseDal>().InstancePerLifetimeScope();
            builder.RegisterType<EfWorkoutProgramTemplateDal>().As<IWorkoutProgramTemplateDal>().InstancePerLifetimeScope();
            builder.RegisterType<EfWorkoutProgramDayDal>().As<IWorkoutProgramDayDal>().InstancePerLifetimeScope();
            builder.RegisterType<EfWorkoutProgramExerciseDal>().As<IWorkoutProgramExerciseDal>().InstancePerLifetimeScope();
            builder.RegisterType<EfMemberWorkoutProgramDal>().As<IMemberWorkoutProgramDal>().InstancePerLifetimeScope();

            // Stateless Utility Services - Singleton lifetime (safe for multi-tenant)
            builder.RegisterType<FileManager>().As<IFileService>().SingleInstance(); // File operations - stateless
            builder.RegisterType<AdvancedRateLimitManager>().As<IAdvancedRateLimitService>().SingleInstance(); // Rate limiting - stateless
            builder.RegisterType<QrCodeEncryptionManager>().As<IQrCodeEncryptionService>().SingleInstance(); // Encryption - stateless
            builder.RegisterType<CacheManager>().As<ICacheService>().SingleInstance(); // Cache - stateless

            // Assembly Registration - Scoped lifetime for interceptors
            var assembly = System.Reflection.Assembly.GetExecutingAssembly();
            builder.RegisterAssemblyTypes(assembly).AsImplementedInterfaces()
                .EnableInterfaceInterceptors(new ProxyGenerationOptions()
                {
                    Selector = new AspectInterceptorSelector()
                }).InstancePerLifetimeScope(); // Changed from SingleInstance to InstancePerLifetimeScope
        }
    }
}
