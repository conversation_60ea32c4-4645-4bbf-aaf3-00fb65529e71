﻿using Core.DataAccess.EntityFramework;
using Core.Entities.Concrete;
using DataAccess.Abstract;
using Entities.Concrete;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DataAccess.Concrete.EntityFramework
{
    public class EfUserDal : EfEntityRepositoryBase<User, GymContext>, IUserDal
    {
        public EfUserDal(GymContext context) : base(context)
        {
        }

        public List<OperationClaim> GetClaims(User user)
        {
            var result = from OperationClaim in _context.OperationClaims
                         join UserOperationClaim in _context.UserOperationClaims
                         on OperationClaim.OperationClaimId equals UserOperationClaim.OperationClaimId
                         where UserOperationClaim.UserId == user.UserID
                         select new OperationClaim { OperationClaimId= OperationClaim.OperationClaimId, Name = OperationClaim.Name };
            return result.ToList();
        }

        /// <summary>
        /// Member r<PERSON><PERSON> olmayan tüm kullanıcıları getirir (Küçük sistemler için)
        /// </summary>
        public List<User> GetNonMembers()
        {
            // Member rolünün ID'sini al (Cache'lenebilir)
            var memberRoleId = _context.OperationClaims
                .Where(oc => oc.Name == "member" && oc.IsActive == true)
                .Select(oc => oc.OperationClaimId)
                .FirstOrDefault();

            if (memberRoleId == 0)
            {
                // Member rolü yoksa tüm aktif kullanıcıları döner
                return _context.Users
                    .Where(u => u.IsActive)
                    .OrderBy(u => u.FirstName)
                    .ThenBy(u => u.LastName)
                    .ToList();
            }

            // Member rolüne sahip kullanıcıların ID'lerini al
            var usersWithMemberRole = _context.UserOperationClaims
                .Where(uoc => uoc.OperationClaimId == memberRoleId && uoc.IsActive == true)
                .Select(uoc => uoc.UserId)
                .ToHashSet(); // HashSet performans için

            // Member rolü olmayan kullanıcıları getir
            return _context.Users
                .Where(u => u.IsActive && !usersWithMemberRole.Contains(u.UserID))
                .OrderBy(u => u.FirstName)
                .ThenBy(u => u.LastName)
                .ToList();
        }

        /// <summary>
        /// Member rolü olmayan kullanıcıları sayfalı olarak getirir (10K+ kullanıcı için optimize)
        /// </summary>
        public List<User> GetNonMembersPaginated(int page, int pageSize, string searchTerm)
        {
            // Member rolünün ID'sini al
            var memberRoleId = _context.OperationClaims
                .Where(oc => oc.Name == "member" && oc.IsActive == true)
                .Select(oc => oc.OperationClaimId)
                .FirstOrDefault();

            var query = _context.Users.AsQueryable();

                // Aktif kullanıcılar
                query = query.Where(u => u.IsActive);

                // Member rolü varsa, member olmayan kullanıcıları filtrele
                if (memberRoleId > 0)
                {
                    var usersWithMemberRole = _context.UserOperationClaims
                        .Where(uoc => uoc.OperationClaimId == memberRoleId && uoc.IsActive == true)
                        .Select(uoc => uoc.UserId);

                    query = query.Where(u => !usersWithMemberRole.Contains(u.UserID));
                }

                // Arama terimi varsa filtrele
                if (!string.IsNullOrEmpty(searchTerm))
                {
                    var lowerSearchTerm = searchTerm.ToLower();
                    query = query.Where(u =>
                        u.FirstName.ToLower().Contains(lowerSearchTerm) ||
                        u.LastName.ToLower().Contains(lowerSearchTerm) ||
                        u.Email.ToLower().Contains(lowerSearchTerm));
                }

                // Sayfalama ve sıralama
                return query
                    .OrderBy(u => u.FirstName)
                    .ThenBy(u => u.LastName)
                    .Skip((page - 1) * pageSize)
                    .Take(pageSize)
                    .ToList();
        }

        /// <summary>
        /// Member rolü olmayan kullanıcı sayısını getirir
        /// </summary>
        public int GetNonMembersCount(string searchTerm)
        {
            // Member rolünün ID'sini al
            var memberRoleId = _context.OperationClaims
                .Where(oc => oc.Name == "member" && oc.IsActive == true)
                .Select(oc => oc.OperationClaimId)
                .FirstOrDefault();

            var query = _context.Users.AsQueryable();

            // Aktif kullanıcılar
            query = query.Where(u => u.IsActive);

            // Member rolü varsa, member olmayan kullanıcıları filtrele
            if (memberRoleId > 0)
            {
                var usersWithMemberRole = _context.UserOperationClaims
                    .Where(uoc => uoc.OperationClaimId == memberRoleId && uoc.IsActive == true)
                    .Select(uoc => uoc.UserId);

                query = query.Where(u => !usersWithMemberRole.Contains(u.UserID));
            }

            // Arama terimi varsa filtrele
            if (!string.IsNullOrEmpty(searchTerm))
            {
                var lowerSearchTerm = searchTerm.ToLower();
                query = query.Where(u =>
                    u.FirstName.ToLower().Contains(lowerSearchTerm) ||
                    u.LastName.ToLower().Contains(lowerSearchTerm) ||
                    u.Email.ToLower().Contains(lowerSearchTerm));
            }

            return query.Count();
        }
       
    }
}
